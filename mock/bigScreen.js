/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from 'mockjs';
import { mockResponse } from './_utils';

const { mock } = mockjs;

function mockLinkAndLoginData(type) {
  const res = {
    link_data: {
      link: `/${type}/59bee04e62be194f5644879d7650aa3a`,
      password: '@word(6)',
    },
    login_data: {
      count: '1',
      'list|2-10': [
        {
          shop_id: '@id',
          login_ip: '@ip',
          login_at: '@datetime',
          user_agent: 'PostmanRuntime/7.28.3',
        },
      ],
    },
  };
  return res;
}

// 大屏数据
const randomValue = (min = 0, max) => parseInt(Math.random() * (max - min + 1) + min, 10);

export default {
  // 获取地图数据
  'POST /Api/Statistics/areaData': (req, res) => {
    const { code } = req.body;

    const mapData = {
      0: [
        { num: '13', city_id: '1', station_num: 4, abbr_name: '上海市', code: '310000' },
        {
          num: '9986279',
          city_id: '334',
          station_num: 6008,
          abbr_name: '浙江省',
          code: '330000',
          out_num: '444',
        },
        {
          num: '1',
          city_id: '335',
          station_num: 3,
          abbr_name: '江苏省',
          code: '320000',
          out_num: '444',
        },
        {
          num: '34105',
          city_id: '681',
          station_num: 27,
          abbr_name: '安徽省',
          code: '340000',
          out_num: '444',
        },
        {
          num: '585825',
          city_id: '5302',
          station_num: 182,
          abbr_name: '北京市',
          code: '110000',
        },
        {
          num: '648',
          city_id: '5342',
          station_num: 4,
          abbr_name: '天津市',
          code: '120000',
          out_num: '444',
        },
        { num: '2', city_id: '5382', station_num: 5, abbr_name: '河北省', code: '130000' },
        { num: '486', city_id: '5430', station_num: 21, abbr_name: '山东省', code: '370000' },
        { num: '109', city_id: '5477', station_num: 19, abbr_name: '江西省', code: '360000' },
        { num: '16830', city_id: '5522', station_num: 17, abbr_name: '福建省', code: '350000' },
        { num: '524', city_id: '5565', station_num: 11, abbr_name: '河南省', code: '410000' },
        { num: '3', city_id: '5591', station_num: 3, abbr_name: '湖北省', code: '420000' },
        { num: '908', city_id: '5619', station_num: 13, abbr_name: '湖南省', code: '430000' },
        { num: '2510', city_id: '5784', station_num: 15, abbr_name: '四川省', code: '510000' },
        { num: '12', city_id: '5876', station_num: 10, abbr_name: '广东省', code: '440000' },
        { num: '3106', city_id: '7603', station_num: 14, abbr_name: '甘肃省', code: '620000' },
        {
          num: '1699977',
          city_id: '7653',
          station_num: 243,
          abbr_name: '宁夏回族自治区',
          code: '640000',
        },
        { num: '5730', city_id: '7769', station_num: 71, abbr_name: '吉林省', code: '220000' },
        { num: '2473', city_id: '7869', station_num: 11, abbr_name: '辽宁省', code: '210000' },
        {
          num: '1',
          city_id: '8018',
          station_num: 4,
          abbr_name: '内蒙古自治区',
          code: '150000',
        },
        {
          num: '109835',
          city_id: '8242',
          station_num: 86,
          abbr_name: '新疆维吾尔自治区',
          code: '650000',
          out_num: '555',
        },
        {
          num: '126446',
          city_id: '9224545',
          station_num: 5,
          abbr_name: '小草科技有限公司',
          code: '',
        },
      ],
      334: [
        { city_id: '330100', abbr_name: '杭州市', num: '233', station_num: '123', code: 330100 },
        { city_id: '331100', abbr_name: '丽水市', num: '300', station_num: '123', code: 331100 },
        { city_id: '330700', abbr_name: '金华市', num: '1000', station_num: '1234', code: 330700 },
        { city_id: '330800', abbr_name: '衢州市', num: '13000', station_num: '1234', code: 330800 },
        { city_id: '331000', abbr_name: '台州市', num: '13000', station_num: '1234', code: 331000 },
      ],
      330100: [
        { city_id: '330106', abbr_name: '西湖区', num: '23', station_num: '20', code: 330106 },
      ],
      331100: [
        { city_id: '331123', abbr_name: '遂昌县', num: '19', station_num: '29', code: 331123 },
      ],
      330700: [
        { city_id: '330702', abbr_name: '婺城区', num: '222', station_num: '55', code: 330702 },
        { city_id: '330703', abbr_name: '金东区', num: '222', station_num: '55', code: 330703 },
        { city_id: '330723', abbr_name: '武义县', num: '222', station_num: '55', code: 330723 },
        { city_id: '330782', abbr_name: '义乌市', num: '222', station_num: '55', code: 330782 },
        {
          abbr_name: '磐安县',
          city_id: '1507',
          code: '330727',
          num: '4653',
          station_num: 44,
        },
      ],
      330800: [
        { city_id: '330803', abbr_name: '衢江区', num: '222', station_num: '55', code: 330803 },
      ],
      331000: [
        { city_id: '331024', abbr_name: '仙居县', num: '222', station_num: '55', code: 331024 },
      ],
      330803: [
        {
          city_id: '330803001000',
          abbr_name: '樟潭街道',
          num: '19',
          order_num: '111',
          out_num: 60,
          station_num: '29',
          code: 330803001000,
        },
        {
          city_id: '330803203000',
          abbr_name: '双桥乡',
          num: '19',
          order_num: '111',
          out_num: 60,
          station_num: '29',
          code: 330803203000,
        },
      ],
      330703: [
        {
          city_id: '330703002000',
          abbr_name: '多湖街道',
          num: '19',
          order_num: '111',
          out_num: 60,
          station_num: '29',
          code: 330703002000,
        },
        {
          city_id: '330703001000',
          abbr_name: '东孝街道',
          num: '19',
          order_num: '111',
          out_num: 60,
          station_num: '29',
          code: 330703001000,
        },
      ],
      330782: [
        {
          city_id: '330782003000',
          abbr_name: '稠江街道',
          num: '19',
          order_num: '111',
          out_num: 60,
          station_num: '29',
          code: 330782003000,
        },
      ],
      331123: [
        {
          city_id: '331123001000',
          abbr_name: '妙高街道',
          num: '19',
          order_num: '111',
          out_num: 60,
          station_num: '29',
          code: 331123001000,
        },
      ],
      331123001000: [
        {
          cm_id: '1494459',
          abbr_name: '包裹未处理仍在原处',
          longitude: 119.275515,
          latitude: 28.583248,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2114659',
          abbr_name: '申通、韵达、百世快递',
          longitude: 119.28,
          latitude: 28.5779,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2223796',
          abbr_name: '上南门—号百货副食品店梦翔',
          longitude: 119.278,
          latitude: 28.5776,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2506424',
          abbr_name: '丽水遂昌县营业部',
          longitude: 119.276,
          latitude: 28.5921,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2692399',
          abbr_name: '新居花园中邮驿站',
          longitude: 119.267,
          latitude: 28.5825,
          num: '76',
          out_num: '54',
          order_num: '0',
        },
        {
          cm_id: '2701066',
          abbr_name: '遂昌中邮总站',
          longitude: 119.311,
          latitude: 28.6179,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2757463',
          abbr_name: '两山大厦A楼中邮驿站',
          longitude: 119.276,
          latitude: 28.5921,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2770984',
          abbr_name: '华鸿锦园中邮驿站',
          longitude: 119.247,
          latitude: 28.5499,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2815535',
          abbr_name: '电信小店',
          longitude: 119.277,
          latitude: 28.5922,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2915103',
          abbr_name: '上江邮政',
          longitude: 119.311,
          latitude: '28.6179',
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2973901',
          abbr_name: '遂昌申通',
          longitude: 119.311,
          latitude: 28.6153,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3030630',
          abbr_name: '和泰碧桂园锦绣城',
          longitude: 119.261,
          latitude: 28.5779,
          num: '71',
          out_num: '38',
          order_num: '0',
        },
        {
          cm_id: '3110192',
          abbr_name: '源口商店',
          longitude: 119.255,
          latitude: 28.5802,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3162974',
          abbr_name: '余家超市中邮驿站',
          longitude: 119.334,
          latitude: 28.6325,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3231863',
          abbr_name: '源口村中邮驿站',
          longitude: 119.251,
          latitude: 28.5682,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3387500',
          abbr_name: '中央首府中邮驿站',
          longitude: 119.27,
          latitude: 28.5842,
          num: '49',
          out_num: '36',
          order_num: '0',
        },
      ],
      331024001000: [
        {
          cm_id: '1494459',
          abbr_name: '包裹未处理仍在原处',
          longitude: 120.765744649,
          latitude: 28.90014707,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2114659',
          abbr_name: '申通、韵达、百世快递',
          longitude: 120.77906044999997,
          latitude: 28.886226383,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2223796',
          abbr_name: '上南门—号百货副食品店梦翔',
          longitude: 120.75670696999998,
          latitude: 28.875856993000014,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
      ],
      330703001000: [
        {
          cm_id: '1494459',
          abbr_name: '包裹未处理仍在原处',
          longitude: 119.707068,
          latitude: 29.081341,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '1494451',
          abbr_name: '包裹未处理仍在原处222',
          longitude: 119.742569,
          latitude: 29.078879,
          num: '10',
          out_num: '20',
          order_num: '30',
        },
        {
          cm_id: '1494454',
          abbr_name: '包裹未处理仍在原处22233333',
          longitude: 119.676954,
          latitude: 29.127822,
          num: '10',
          out_num: '20',
          order_num: '30',
        },
        {
          cm_id: '1494453',
          abbr_name: '4444',
          longitude: 119.690567,
          latitude: 29.118202,
          num: '10',
          out_num: '20',
          order_num: '30',
        },
      ],
      1976: [
        {
          city_id: '331123001000',
          abbr_name: '妙高街道',
          num: '19',
          order_num: '111',
          out_num: 60,
          station_num: '29',
          code: 331123001000,
        },
      ],

      330702: [
        {
          station_num: '1',
          abbr_name: '城东街道',
          code: '330702001000',
          num: 0,
          out_num: 0,
          city_id: '330702001000',
          order_num: '0',
        },
        {
          station_num: '3',
          abbr_name: '城中街道',
          code: '330702002000',
          num: 0,
          out_num: 0,
          city_id: '330702002000',
          order_num: '0',
        },
        {
          station_num: '2',
          abbr_name: '城西街道',
          code: '330702003000',
          num: 0,
          out_num: 0,
          city_id: '330702003000',
          order_num: '0',
        },
        {
          station_num: '17',
          abbr_name: '城北街道',
          code: '330702004000',
          num: 1379,
          out_num: 1235,
          city_id: '330702004000',
          order_num: '0',
        },
        {
          station_num: '5',
          abbr_name: '江南街道',
          code: '330702005000',
          num: 373,
          out_num: 378,
          city_id: '330702005000',
          order_num: '0',
        },
        {
          station_num: '14',
          abbr_name: '三江街道',
          code: '330702006000',
          num: 298,
          out_num: 262,
          city_id: '330702006000',
          order_num: '0',
        },
        {
          station_num: '7',
          abbr_name: '西关街道',
          code: '330702007000',
          num: 10062,
          out_num: 9073,
          city_id: '330702007000',
          order_num: '2',
        },
        {
          station_num: '25',
          abbr_name: '秋滨街道',
          code: '330702008000',
          num: 1132,
          out_num: 992,
          city_id: '330702008000',
          order_num: '1',
        },
        {
          station_num: '13',
          abbr_name: '新狮街道',
          code: '330702009000',
          num: 2025,
          out_num: 1583,
          city_id: '330702009000',
          order_num: '0',
        },
        {
          station_num: '11',
          abbr_name: '罗店镇',
          code: '330702100000',
          num: 693,
          out_num: 1305,
          city_id: '330702100000',
          order_num: '0',
        },
        {
          station_num: '2',
          abbr_name: '雅畈镇',
          code: '330702101000',
          num: 14,
          out_num: 4,
          city_id: '330702101000',
          order_num: '0',
        },
        {
          station_num: '2',
          abbr_name: '安地镇',
          code: '330702102000',
          num: 1,
          out_num: 1,
          city_id: '330702102000',
          order_num: '0',
        },
        {
          station_num: '25',
          abbr_name: '白龙桥镇',
          code: '330702103000',
          num: 1546,
          out_num: 1431,
          city_id: '330702103000',
          order_num: '0',
        },
        {
          station_num: '1',
          abbr_name: '琅琊镇',
          code: '330702104000',
          num: 498,
          out_num: 493,
          city_id: '330702104000',
          order_num: '0',
        },
        {
          station_num: '3',
          abbr_name: '蒋堂镇',
          code: '330702105000',
          num: 56,
          out_num: 70,
          city_id: '330702105000',
          order_num: '0',
        },
        {
          station_num: '10',
          abbr_name: '汤溪镇',
          code: '330702106000',
          num: 165,
          out_num: 141,
          city_id: '330702106000',
          order_num: '0',
        },
        {
          station_num: '3',
          abbr_name: '罗埠镇',
          code: '330702107000',
          num: 77,
          out_num: 77,
          city_id: '330702107000',
          order_num: '0',
        },
        {
          station_num: '1',
          abbr_name: '洋埠镇',
          code: '330702108000',
          num: 474,
          out_num: 434,
          city_id: '330702108000',
          order_num: '0',
        },
        {
          station_num: '5',
          abbr_name: '乾西乡',
          code: '330702201000',
          num: 314,
          out_num: 293,
          city_id: '330702201000',
          order_num: '0',
        },
        {
          station_num: '1',
          abbr_name: '竹马乡',
          code: '330702202000',
          num: 0,
          out_num: 0,
          city_id: '330702202000',
          order_num: '0',
        },
        {
          station_num: '1',
          abbr_name: '长山乡',
          code: '330702203000',
          num: 3,
          out_num: 0,
          city_id: '330702203000',
          order_num: '0',
        },
        {
          station_num: '2',
          abbr_name: '塔石乡',
          code: '330702206000',
          num: 0,
          out_num: 0,
          city_id: '330702206000',
          order_num: '0',
        },
        {
          station_num: '1',
          abbr_name: '莘畈乡',
          code: '330702208000',
          num: 5,
          out_num: 3,
          city_id: '330702208000',
          order_num: '0',
        },
        {
          station_num: '3',
          abbr_name: '苏孟乡',
          code: '330702209000',
          num: 128,
          out_num: 156,
          city_id: '330702209000',
          order_num: '0',
        },
      ],

      330702004000: [
        {
          cm_id: '2940006',
          abbr_name: '北苑小区中邮驿站',
          longitude: 119.651,
          latitude: 29.1169,
          num: '149',
          out_num: '29',
          order_num: '0',
        },
        {
          cm_id: '3332010',
          abbr_name: '军营驿站南区',
          longitude: 119.639,
          latitude: 29.1156,
          num: '255',
          out_num: '575',
          order_num: '0',
        },
        {
          cm_id: '3334982',
          abbr_name: '邮政大楼中邮驿站',
          longitude: 119.633,
          latitude: 29.1091,
          num: '20',
          out_num: '9',
          order_num: '0',
        },
        {
          cm_id: '3683835',
          abbr_name: '家美超市',
          longitude: 119.644,
          latitude: 29.111,
          num: '2',
          out_num: '2',
          order_num: '0',
        },
        {
          cm_id: '3693181',
          abbr_name: '联球大厦中邮驿站',
          longitude: 119.639,
          latitude: 29.1079,
          num: '2',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3696176',
          abbr_name: '安宁烟酒中邮驿站',
          longitude: 119.633,
          latitude: 29.1076,
          num: '2',
          out_num: '2',
          order_num: '0',
        },
        {
          cm_id: '3703515',
          abbr_name: '御景花园',
          longitude: 119.633,
          latitude: 29.1083,
          num: '25',
          out_num: '7',
          order_num: '0',
        },
        {
          cm_id: '3708705',
          abbr_name: '银河花园中邮驿站',
          longitude: 119.634,
          latitude: 29.1095,
          num: '5',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3748329',
          abbr_name: '城北街道后城里街399号',
          longitude: 119.637,
          latitude: 29.1097,
          num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3774355',
          abbr_name: '金竹苑中邮驿站',
          longitude: 119.62,
          latitude: 29.1056,
          num: '85',
          out_num: '44',
          order_num: '0',
        },
      ],

      1507: [
        {
          station_num: '1',
          abbr_name: '维新乡',
          code: '000000000000',
          num: 0,
          out_num: 3,
          city_id: '000000000000',
          order_num: '0',
        },
        {
          station_num: '9',
          abbr_name: '安文镇',
          code: '330727100000',
          num: 0,
          out_num: 494,
          city_id: '330727100000',
          order_num: '0',
        },
        {
          station_num: '2',
          abbr_name: '新渥街道',
          code: '330727101000',
          num: 0,
          out_num: 277,
          city_id: '330727101000',
          order_num: '0',
        },
        {
          station_num: '4',
          abbr_name: '尖山镇',
          code: '330727102000',
          num: 0,
          out_num: 249,
          city_id: '330727102000',
          order_num: '0',
        },
        {
          station_num: '1',
          abbr_name: '仁川镇',
          code: '330727103000',
          num: 0,
          out_num: 0,
          city_id: '330727103000',
          order_num: '0',
        },
        {
          station_num: '2',
          abbr_name: '大盘镇',
          code: '330727104000',
          num: 667,
          out_num: 396,
          city_id: '330727104000',
          order_num: '0',
        },
        {
          station_num: '1',
          abbr_name: '方前镇',
          code: '330727105000',
          num: 343,
          out_num: 493,
          city_id: '330727105000',
          order_num: '0',
        },
        {
          station_num: '1',
          abbr_name: '玉山镇',
          code: '330727106000',
          num: 381,
          out_num: 342,
          city_id: '330727106000',
          order_num: '0',
        },
        {
          station_num: '2',
          abbr_name: '尚湖镇',
          code: '330727107000',
          num: 415,
          out_num: 611,
          city_id: '330727107000',
          order_num: '0',
        },
        {
          station_num: '1',
          abbr_name: '冷水镇',
          code: '330727108000',
          num: 0,
          out_num: 0,
          city_id: '330727108000',
          order_num: '0',
        },
        {
          station_num: '1',
          abbr_name: '双峰乡',
          code: '330727201000',
          num: 0,
          out_num: 4,
          city_id: '330727201000',
          order_num: '0',
        },
        {
          station_num: '12',
          abbr_name: '双溪乡',
          code: '330727203000',
          num: 523,
          out_num: 515,
          city_id: '330727203000',
          order_num: '0',
        },
        {
          station_num: '4',
          abbr_name: '窈川乡',
          code: '330727205000',
          num: 592,
          out_num: 852,
          city_id: '330727205000',
          order_num: '0',
        },
        {
          station_num: '2',
          abbr_name: '盘峰乡',
          code: '330727206000',
          num: 0,
          out_num: 0,
          city_id: '330727206000',
          order_num: '0',
        },
        {
          station_num: '1',
          abbr_name: '九和乡',
          code: '330727211000',
          num: 0,
          out_num: 21,
          city_id: '330727211000',
          order_num: '0',
        },
      ],
      331024: [
        {
          city_id: '331024001000',
          abbr_name: '安洲街道',
          num: '19',
          order_num: '111',
          out_num: 60,
          station_num: '29',
          code: 331024001000,
        },
      ],
    };

    res.send({
      code: 0,
      msg: 'success',
      data: mapData[code],
    });
  },
  // 获取实时出入库、新增站点、寄件订单数
  'POST /Api/Statistics/count': (req, res) => {
    const { code = 0 } = req.body;

    const dataMap = {
      0: {
        total: 1000000,
        yz_total: 1000000,
      },
      334: {
        total: 0,
        yz_total: 0,
      },
      330100: {
        total: 200,
        yz_total: 200,
      },
      331100: {
        total: 600,
        yz_total: 800,
      },
      331123: {
        total: 0,
        yz_total: 0,
      },
      331123001000: {
        total: 31000,
        yz_total: 44000,
      },
    };

    res.send({
      code: 0,
      msg: 'success',
      data: dataMap[code],
    });
  },
  // 大屏，昨日数据看版
  'POST /Api/Statistics/yesterdayBoard': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        in_data: {
          num: 40,
          rate: -10,
        },
        out_data: {
          num: 40,
          rate: 29,
        },
        back_data: {
          num: 40,
          rate: -15,
        },
        notice_data: {
          num: 98.9,
          rate: 10,
        },
      },
    });
  },
  // 大屏，昨日入库数
  'POST /Api/Statistics/rank': (req, res) => {
    const { type, code } = req.body;
    const length = code == 0 ? 5 : 3;

    const orderList = [];
    for (let i = 0; i < length; i += 1) {
      orderList.push({
        abbr_name: `${type}_区域${i + 1}___${code}`,
        num: type === 'out_rate' ? `1${i}` : `1${i}23`,
        city_id: type === 'out_rate' ? `1${i}` : `1${i}23`,
      });
    }
    res.send({
      code: 0,
      msg: '获取成功',
      data: orderList,
    });
  },
  // 7日出入库数
  'POST /Api/Statistics/storageSummary': (req, res) => {
    res.send({
      code: 0,
      msg: '获取成功',
      data: [
        {
          date: '2018-09-29',
          in_num: 1500,
          out_num: 2,
          back_num: 0,
        },
        {
          date: '2018-10-09',
          in_num: 2651,
          out_num: 2,
          back_num: '0',
        },
        {
          date: '2018-10-10',
          in_num: 3245,
          out_num: 2,
          back_num: '0',
        },
        {
          date: '2018-10-11',
          in_num: 4231,
          out_num: 2,
          back_num: '0',
        },
        {
          date: '2018-10-13',
          in_num: 2311,
          out_num: 2,
          back_num: '0',
        },
        {
          date: '2018-10-14',
          in_num: 3184,
          out_num: 2,
          back_num: '0',
        },
        {
          date: '2018-10-15',
          in_num: 2015,
          out_num: 2,
          back_num: '0',
        },
        {
          date: '2018-10-16',
          in_num: 2641,
          out_num: 2,
          back_num: '0',
        },
      ],
    });
  },
  // 近7日共配扫描数
  'POST /Api/Statistics/pdaSummary': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        { days: '2023-03-06', count: 0 },
        { days: '2023-03-05', count: 214 },
        { days: '2023-03-04', count: 0 },
        { days: '2023-03-03', count: 0 },
        { days: '2023-03-02', count: 428 },
        { days: '2023-03-01', count: 186 },
        { days: '2023-02-28', count: 2455 },
      ],
    });
  },
  // 7日入库品牌占比
  'POST /Api/Statistics/storagePic': (req, res) => {
    const { type } = req.body;

    switch (type) {
      case 'in':
        res.send({
          code: 0,
          msg: '成功',
          data: [
            {
              brand: '中通',
              num: 56818,
            },
            {
              brand: 'EMS',
              num: 54640,
            },
            {
              brand: '圆通',
              num: 33290,
            },
            {
              brand: '韵达',
              num: 22662,
            },
            {
              brand: '申通',
              num: 21282,
            },
            {
              brand: '极兔',
              num: 19903,
            },
            {
              brand: '顺丰',
              num: 3709,
            },
            {
              brand: '邮政',
              num: 2674,
            },
            {
              brand: '京东',
              num: 1360,
            },
            {
              brand: '其他',
              num: 2407,
            },
          ],
        });
        break;
      case 'out':
        res.send({
          code: 0,
          msg: '成功',
          data: [
            {
              brand: '安能out',
              num: 9,
            },
            {
              brand: '德邦',
              num: 8,
            },
            {
              brand: 'EMS',
              num: 7,
            },
            {
              brand: '国通',
              num: 6,
            },
            {
              brand: '百世',
              num: 5,
            },
            {
              brand: '汇文',
              num: 4,
            },
            {
              brand: '京东',
              num: 3,
            },
            {
              brand: '快捷',
              num: 2,
            },
            {
              brand: '顺丰',
              num: 1,
            },
          ],
        });
        break;
      case 'order':
        res.send({
          code: 0,
          msg: '成功',
          data: [
            {
              brand: '安能order',
              num: 9,
            },
            {
              brand: '德邦',
              num: 8,
            },
            {
              brand: 'EMS',
              num: 7,
            },
            {
              brand: '国通',
              num: 6,
            },
            {
              brand: '百世',
              num: 5,
            },
            {
              brand: '汇文',
              num: 4,
            },
            {
              brand: '京东',
              num: 3,
            },
            {
              brand: '快捷',
              num: 2,
            },
            {
              brand: '顺丰',
              num: 1,
            },
          ],
        });
        break;
      default:
        res.send({
          code: 0,
          msg: '成功',
          data: [
            {
              brand: '安能',
              in_num: 1,
              out_num: 2,
              order_num: 22,
            },
            {
              brand: '德邦',
              in_num: 1,
              out_num: 2,
              order_num: 34,
            },
            {
              brand: 'EMS',
              in_num: 1,
              out_num: 2,
              order_num: 25,
            },
            {
              brand: '国通',
              in_num: 1,
              out_num: 4,
              order_num: 28,
            },
            {
              brand: '百世',
              in_num: 2,
              out_num: 8,
              order_num: 23,
            },
            {
              brand: '汇文',
              in_num: 1,
              out_num: 2,
              order_num: 42,
            },
            {
              brand: '京东',
              in_num: 1,
              out_num: 2,
              order_num: 21,
            },
            {
              brand: '快捷',
              in_num: 1,
              out_num: 2,
              order_num: 9,
            },
            {
              brand: '顺丰',
              in_num: 6,
              out_num: 6,
              order_num: 7,
            },
            {
              brand: '苏宁',
              in_num: 1,
              out_num: 0,
              order_num: 3,
            },
            {
              brand: '申通',
              in_num: 3,
              out_num: 3,
              order_num: 2,
            },
            {
              brand: '其他',
              in_num: 2,
              out_num: 1,
              order_num: 2,
            },
          ],
        });
        break;
    }
  },
  // 获取下属驿站列表
  'POST /Api/Statistics/getSubDaksList': (req, res) => {
    const list = [];
    Array.from({ length: 70 }).forEach((v, i) => {
      list.push({
        id: i,
        cm_id: i,
        kb_id: i,
        company_name: `下属驿站${i}`,
        phone: i,
      });
    });

    res.send({
      code: 0,
      msg: 'success',
      data: list,
    });
  },
  // 大屏，宇视-获取有设备的驿站id
  'POST /Api/Statistics/getHasVideoCmId': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: '2127890,1957041',
    });
  },
  // 大屏，宇视-获取宇视通道号
  'POST /Api/Statistics/getCityChannelList': (req, res) => {
    const arr = [];
    Array.from({
      length: 16,
    }).forEach((_, index) => {
      arr.push(index);
    });
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          total: 30,
          channelList: [
            {
              deviceSerial: '1',
              channelNo: '1',
              channelName: '机位1',
              status: '4',
            },
            {
              deviceSerial: '1',
              channelNo: '2',
              channelName: '机位2',
              status: '4',
            },
          ],
          device_name: '设备名称',
          cm_id: '0000',
          cm_name: '驿站名称',
        },
        {
          total: 20,
          channelList: [
            {
              deviceSerial: '1',
              channelNo: '1',
              channelName: '机位3',
              status: '4',
            },
            {
              deviceSerial: '1',
              channelNo: '2',
              channelName: '机位4',
              status: '4',
            },
          ],
          device_name: '设备名称1',
          cm_id: '00001',
          cm_name: '驿站名称1',
        },
      ],
    });
  },
  // 大屏，宇视-获取播放视频地址
  'POST /Api/Statistics/playCityVideos': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        hlsUrl:
          'https://test-videos.co.uk/vids/bigbuckbunny/webm/vp8/360/Big_Buck_Bunny_360_10s_1MB.webm',
        // "https://livepull1.uniview.com/live/210235c39mf211000005000103715930.m3u8?txTime=60FEFB2E&txSecret=222b8062715c5ddf803530d64357a68b&codec=264",
        status: 'ok',
      },
    });
  },
  // 大屏，宇视-开启推流
  'POST /Api/Statistics/videoCityStart': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        liveId: '123',
      },
    });
  },
  // 大屏，宇视-结束推流
  'POST /Api/Statistics/videoCityStop': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 新零售大屏，获取顶部实时数据
  'POST /Api/Statistics/getTopStat': (req, res) => {
    setTimeout(() => {
      res.send({
        code: 0,
        msg: 'success',
        data: {
          in_num: {
            today_last: 889000,
            today: 999000,
            yesterday: 8000,
            dod_ratio: 2.23,
          },
          express_num: {
            today: 900000,
            yesterday: 8000,
            dod_ratio: null,
          },
          stage_num: {
            yesterday: 1000.01,
            total: 123131,
          },
          fans_num: {
            yesterday: 8000,
            total: 24214124,
          },
        },
      });
    }, 1500);
  },
  // 新零售大屏，获取实时图表数据
  'POST /Api/Statistics/getTimelineStat': (req, res) => {
    const date = new Date();
    // 时长间隔 ms
    const size = 10 * 60 * 1000;
    const label = v => (v < 10 ? `0${v}` : v);

    const arr = Array.from(
      {
        length: 48,
      },
      () => {
        date.setTime(date.getTime() + size);
        return {
          date: `2021-11-25 ${label(date.getHours())}:${label(date.getMinutes())}`,
          arrive_num: randomValue(800, 1000),
          send_num: randomValue(300, 500),
          in_num: randomValue(500, 600),
          out_num: randomValue(600, 800),
        };
      },
    );

    setTimeout(() => {
      res.send({
        code: 0,
        msg: 'success',
        data: arr,
      });
    }, 1500);
  },
  // 新零售大屏，每日入库量
  'POST /Api/Statistics/getStorageInNumSevenDays': (req, res) => {
    const arr = Array.from(
      {
        length: 7,
      },
      (_, index) => ({
        date: `2021-12-1${index}`,
        num: randomValue(800, 100000),
        cabinet: randomValue(800, 100000),
      }),
    );

    res.send({
      code: 0,
      msg: 'success',
      data: arr,
    });
  },
  // 新零售大屏，每日寄件量
  'POST /Api/Statistics/getExpressStatSevenDays': (req, res) => {
    const arr = Array.from(
      {
        length: 7,
      },
      (_, index) => ({
        date: `2021-12-1${index}`,
        num: randomValue(800, 100000),
      }),
    );

    res.send({
      code: 0,
      msg: 'success',
      data: arr,
    });
  },
  // 新零售大屏，每日出库率
  'POST /Api/Statistics/getStorageOutRateSevenDays': (req, res) => {
    const arr = Array.from(
      {
        length: 7,
      },
      (_, index) => ({
        date: `2021-12-1${index}`,
        num: randomValue(70, 100),
      }),
    );

    res.send({
      code: 0,
      msg: 'success',
      data: arr,
    });
  },
  // 新零售大屏，出库率(3日)排名
  'POST /Api/Statistics/getStorageOutRateRankYesterday': (req, res) => {
    const list = Array.from({ length: 7 }, (_, index) => ({
      inn_name: `驿站名称${index}`,
      out_rate: `9${7 - index}`,
    }));

    res.send({
      code: 0,
      msg: 'success',
      data: list,
    });
  },
  // 新零售大屏，昨日入库品牌占比
  'POST /Api/Statistics/getStorageInBrandRatioYesterday': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          brand: '申通',
          ratio: 30.0,
        },
        {
          brand: '韵达',
          ratio: 20,
        },
        {
          brand: '中通',
          ratio: 20,
        },
        {
          brand: '邮政',
          ratio: 15,
        },
        {
          brand: '圆通',
          ratio: 10,
        },
        {
          brand: '极兔',
          ratio: 0,
        },
        {
          brand: '京东',
          ratio: 0,
        },
      ],
    });
  },
  // 新零售大屏，昨日入库量排名
  'POST /Api/Statistics/getStorageInNumRankYesterday': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          inn_name: '驿站A驿站',
          in_num: 23000,
        },
        {
          inn_name: '驿站B驿站',
          in_num: 21000,
        },
        {
          inn_name: '驿站C驿站',
          in_num: 15232,
        },
        {
          inn_name: '驿站D驿站',
          in_num: 12020,
        },
        {
          inn_name: '驿站E驿站',
          in_num: 5031,
        },
        {
          inn_name: '驿站F',
          in_num: 2312,
        },
        {
          inn_name: '驿站G',
          in_num: 190,
        },
      ],
    });
  },
  // 新零售大屏，登录
  'POST /Api/ShareLink/validate': mockResponse(
    {
      token: '@word(20)',
    },
    null,
  ),
  // 新零售大屏，获取已连接用户列表
  'POST /Api/ShareLink/getShareInfo': mockResponse(mockLinkAndLoginData('dashboard'), null),
  // 新零售大屏，生成分享链接和密码
  'POST /Api/ShareLink/generate': mockResponse(
    {
      link: '/dashboard/59bee04e62be194f5644879d7650aa3a',
      password: '@word(6)',
    },
    null,
  ),
  // 新零售大屏，获取登录用户信息
  'POST /Api/ShareLink/getLeagueInfo': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        branch: [
          // {
          //   id: '0',
          //   name: '中国邮政集团有限公司',
          //   level: '0',
          //   pid: '-1',
          // },
          // {
          //   id: '334',
          //   name: '浙江省',
          //   level: '1',
          //   pid: '0',
          // },
          // {
          //   id: '1899',
          //   name: '丽水市',
          //   level: '2',
          //   pid: '334',
          // },
          // {
          //   id: '1976',
          //   name: '遂昌县',
          //   level: '3',
          //   pid: '1899',
          // },
        ],
        branchCode: '0',
        branchId: '0',
        branchLevel: '0',
        branchName: '中国邮政集团有限公司浙江省丽水市遂昌县分公司',
        is_active: '0',
        kb_id: '165466977',
        name: '遂昌大屏测试',
        phone: '17911111111',
        shop_id: 6380,
        shortName: '中国邮政集团有限公司遂昌县分公司',
        user_type: '2',
        shop_name: '快大宝',
      },
    });
  },
  // 新零售大屏，登录 - 监控视频
  'POST /Api/ShareLink/validateVideo': mockResponse(
    {
      token: '@word(20)',
    },
    null,
  ),
  // 新零售大屏，获取已连接用户列表 - 监控视频
  'POST /Api/ShareLink/getShareInfoVideo': mockResponse(mockLinkAndLoginData('video'), null),
  // 新零售大屏，生成分享链接和密码 - 监控视频
  'POST /Api/ShareLink/generateVideo': mockResponse(
    {
      link: '/video/59bee04e62be194f5644879d7650aa3a',
      password: '@word(6)',
    },
    null,
  ),
  // 新零售大屏，获取登录用户信息 - 监控视频
  'POST /Api/ShareLink/getLeagueInfoVideo': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        branch: [
          // {
          //   id: '0',
          //   name: '中国邮政集团有限公司',
          //   level: '0',
          //   pid: '-1',
          // },
          // {
          //   id: '334',
          //   name: '浙江省',
          //   level: '1',
          //   pid: '0',
          // },
          // {
          //   id: '1899',
          //   name: '丽水市',
          //   level: '2',
          //   pid: '334',
          // },
          // {
          //   id: '1976',
          //   name: '遂昌县',
          //   level: '3',
          //   pid: '1899',
          // },
        ],
        branchCode: '0',
        branchId: '0',
        branchLevel: '0',
        branchName: '中国邮政集团有限公司浙江省丽水市遂昌县分公司',
        is_active: '0',
        kb_id: '165466977',
        name: '遂昌大屏测试',
        phone: '17911111111',
        shop_id: 6380,
        shortName: '中国邮政集团有限公司遂昌县分公司',
        user_type: '2',
        shop_name: '左侧大标题',
        quantity: '6',
      },
    });
  },
  // 新零售大屏，登录 - 监控视频
  'POST /Api/ShareLink/addVideoPageConf': mockResponse(),
  // 中邮大屏，获取驿站近7天数据
  'POST /Api/Statistics/innData': (req, res) => {
    res.send(
      mock({
        msg: '成功',
        code: 0,
        data: {
          'data|7': [
            {
              date: '@date("yyyy-MM-dd")',
              in_num: randomValue(1, 1000),
              out_num: randomValue(1, 1000),
              order_num: randomValue(1, 1000),
            },
          ],
          inn_name: 'xxxxx驿站',
          concat_name: '张三',
          concat_phone: '13388889999',
          concat_location: '上海市上海市长宁区建滔广场',
        },
      }),
    );
  },
  // 中邮共配大屏，昨日数据看版，昨日到派签数据
  'POST /v1/TotalDistribution/ChinaPostTongji/getYdayTongjiData': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data: {
          dj: {
            num: '@integer(100,10000)',
            rate: '@integer(-10,10)',
          },
          pj: {
            num: '@integer(100,10000)',
            rate: '@integer(-10,10)',
          },
          qs: {
            num: '@integer(100,10000)',
            rate: '@integer(-10,10)',
          },
        },
      }),
    );
  },
  // 中邮共配大屏，昨日每小时到件量排名
  'POST /v1/TotalDistribution/ChinaPostTongji/getYdayEveryHourArrivalRanking': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data: [
          {
            sum_num: '17000',
            hour: '13:00',
          },
          {
            sum_num: '16000',
            hour: '12:00',
          },
          {
            sum_num: '15000',
            hour: '11:00',
          },
          {
            sum_num: '14000',
            hour: '10:00',
          },
          {
            sum_num: '13000',
            hour: '9:00',
          },
          {
            sum_num: '12000',
            hour: '8:00',
          },
          {
            sum_num: '11000',
            hour: '7:00',
          },
        ],
      }),
    );
  },
  // 中邮共配大屏，昨日到件品牌排名
  'POST /v1/TotalDistribution/ChinaPostTongji/getYdayArrivalBrandRanking': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data: [
          {
            sum_num: '@integer(100,10000)',
            brand: '顺丰',
          },
          {
            sum_num: '@integer(100,10000)',
            brand: '申通',
          },
          {
            sum_num: '@integer(100,10000)',
            brand: '极兔',
          },
          {
            sum_num: '@integer(100,10000)',
            brand: '邮政',
          },
          {
            sum_num: '@integer(100,10000)',
            brand: '中通',
          },
          {
            sum_num: '@integer(100,10000)',
            brand: '韵达',
          },
          {
            sum_num: '@integer(100,10000)',
            brand: '其他',
          },
        ],
      }),
    );
  },
  // 中邮共配大屏，近七日到件数量
  'POST /v1/TotalDistribution/ChinaPostTongji/getweekArrivalSum': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data: [
          {
            sum_num: '@integer(100,1000000)',
            date: '2023-05-21',
          },
          {
            sum_num: '@integer(100,1000000)',
            date: '2023-05-22',
          },
          {
            sum_num: '@integer(100,1000000)',
            date: '2023-05-23',
          },
          {
            sum_num: '@integer(100,1000000)',
            date: '2023-05-24',
          },
          {
            sum_num: '@integer(100,1000000)',
            date: '2023-05-25',
          },
          {
            sum_num: '@integer(100,1000000)',
            date: '2023-05-26',
          },
          {
            sum_num: '@integer(100,1000000)',
            date: '2023-05-27',
          },
        ],
      }),
    );
  },
  // 中邮共配大屏，近七日到件品牌占比
  'POST /v1/TotalDistribution/ChinaPostTongji/getweekArrivalBrandRanking': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data: [
          {
            sum_num: '@integer(100,10000)',
            brand: '顺丰',
            zh_cn: '顺丰',
          },
          {
            sum_num: '@integer(100,10000)',
            brand: '申通',
            zh_cn: '申通',
          },
          {
            sum_num: '@integer(100,10000)',
            brand: '极兔',
            zh_cn: '极兔',
          },
          {
            sum_num: '@integer(100,10000)',
            brand: '邮政',
            zh_cn: '邮政',
          },
          {
            sum_num: '@integer(100,10000)',
            brand: '中通',
            zh_cn: '中通',
          },
          {
            sum_num: '@integer(100,10000)',
            brand: '韵达',
            zh_cn: '韵达',
          },
          {
            sum_num: '@integer(100,10000)',
            brand: '其他',
            zh_cn: '其他',
          },
        ],
      }),
    );
  },
  // 中邮共配大屏，今日实时数据
  'POST /v1/TotalDistribution/ChinaPostTongji/getTodayTongjiSum': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data: {
          dj: '@integer(100,10000)',
          pj: '@integer(100,10000)',
          qs: '@integer(100,10000)',
          ems_dj: '@integer(100,10000)',
          ems_pj: '@integer(100,10000)',
          ems_qs: '@integer(100,10000)',
          custom: '@integer(100,10000)',
          ems_custom: '@integer(100,10000)',
        },
      }),
    );
  },
  // 中邮共配大屏，获取地区数据
  'POST /v1/TotalDistribution/ChinaPostTongji/getHeatMapData': (req, res) => {
    const { code } = req.body;

    const mapData = {
      0: [
        { sum_num: '13', city_id: '1', count: 4, areaName: '上海市', code: '310000' },
        {
          sum_num: '9986279',
          city_id: '334',
          count: 6008,
          areaName: '浙江省',
          code: '330000',
        },
        { sum_num: '1', city_id: '335', count: 3, areaName: '江苏省', code: '320000' },
        { sum_num: '34105', city_id: '681', count: 27, areaName: '安徽省', code: '340000' },
        {
          sum_num: '585825',
          city_id: '5302',
          count: 182,
          areaName: '北京市',
          code: '110000',
        },
        { sum_num: '648', city_id: '5342', count: 4, areaName: '天津市', code: '120000' },
        { sum_num: '2', city_id: '5382', count: 5, areaName: '河北省', code: '130000' },
        { sum_num: '486', city_id: '5430', count: 21, areaName: '山东省', code: '370000' },
        { sum_num: '109', city_id: '5477', count: 19, areaName: '江西省', code: '360000' },
        { sum_num: '16830', city_id: '5522', count: 17, areaName: '福建省', code: '350000' },
        { sum_num: '524', city_id: '5565', count: 11, areaName: '河南省', code: '410000' },
        { sum_num: '3', city_id: '5591', count: 3, areaName: '湖北省', code: '420000' },
        { sum_num: '908', city_id: '5619', count: 13, areaName: '湖南省', code: '430000' },
        { sum_num: '2510', city_id: '5784', count: 15, areaName: '四川省', code: '510000' },
        { sum_num: '12', city_id: '5876', count: 10, areaName: '广东省', code: '440000' },
        { sum_num: '3106', city_id: '7603', count: 14, areaName: '甘肃省', code: '620000' },
        {
          sum_num: '1699977',
          city_id: '7653',
          count: 243,
          areaName: '宁夏回族自治区',
          code: '640000',
        },
        { sum_num: '5730', city_id: '7769', count: 71, areaName: '吉林省', code: '220000' },
        { sum_num: '2473', city_id: '7869', count: 11, areaName: '辽宁省', code: '210000' },
        {
          sum_num: '1',
          city_id: '8018',
          count: 4,
          areaName: '内蒙古自治区',
          code: '150000',
        },
        {
          sum_num: '109835',
          city_id: '8242',
          count: 86,
          areaName: '新疆维吾尔自治区',
          code: '650000',
        },
        {
          sum_num: '126446',
          city_id: '9224545',
          count: 5,
          areaName: '小草科技有限公司',
          code: '',
        },
      ],
      334: [
        { city_id: '330100', areaName: '杭州市', sum_num: '233', count: '123', code: 330100 },
        { city_id: '331100', areaName: '丽水市', sum_num: '2000', count: '123', code: 331100 },
        { city_id: '330700', areaName: '金华市', sum_num: '7000', count: '1234', code: 330700 },
        { city_id: '330800', areaName: '衢州市', sum_num: '13000', count: '1234', code: 330800 },
        { city_id: '331000', abbr_name: '台州市', sum_num: '13000', count: '1234', code: 331000 },
      ],
      330100: [{ city_id: '330106', areaName: '西湖区', sum_num: '23', count: '20', code: 330106 }],
      331100: [{ city_id: '331123', areaName: '遂昌县', sum_num: '19', count: '29', code: 331123 }],
      331000: [
        { city_id: '331024', areaName: '仙居县', sum_num: '222', count: '55', code: 331024 },
      ],
      330700: [
        { city_id: '330702', areaName: '婺城区', sum_num: '222', count: '55', code: 330702 },
        { city_id: '330703', areaName: '金东区', sum_num: '720', count: '55', code: 330703 },
        { city_id: '330723', areaName: '武义县', sum_num: '1220', count: '55', code: 330723 },
        { city_id: '330782', areaName: '义乌市', sum_num: '22200', count: '55', code: 330782 },
        {
          areaName: '磐安县',
          city_id: '1507',
          code: '330727',
          sum_num: '4653',
          count: 44,
        },
      ],
      330800: [
        {
          city_id: '330803',
          areaName: '衢江区',
          sum_num: '222',
          count: '55',
          code: 330803,
          longitude: 118.993941,
          latitude: 28.982877,
        },
        {
          city_id: '330802',
          areaName: '柯城区',
          sum_num: '2222',
          count: '551',
          code: 330802,
        },
      ],
      330803: [
        {
          city_id: '330803001000',
          areaName: '樟潭街道',
          sum_num: '19',
          order_num: '111',
          out_num: 60,
          count: '29',
          code: 330803001000,
        },
        {
          city_id: '330803203000',
          areaName: '双桥乡',
          sum_num: '19',
          order_num: '111',
          out_num: 60,
          count: '29',
          code: 330803203000,
        },
      ],
      330703: [
        {
          city_id: '330703002000',
          areaName: '多湖街道',
          sum_num: '19',
          order_num: '111',
          out_num: 60,
          count: '29',
          code: 330703002000,
        },
        {
          city_id: '330703001000',
          areaName: '东孝街道',
          sum_num: '19',
          order_num: '111',
          out_num: 60,
          count: '29',
          code: 330703001000,
        },
      ],
      330782: [
        {
          city_id: '330782003000',
          areaName: '稠江街道',
          sum_num: '19',
          order_num: '111',
          out_num: 60,
          count: '29',
          code: 330782003000,
        },
      ],
      331123: [
        {
          city_id: '331123001000',
          areaName: '妙高街道',
          sum_num: '19',
          order_num: '111',
          out_num: 60,
          count: '29',
          code: 331123001000,
        },
      ],
      331123001000: [
        {
          cm_id: '1494459',
          areaName: '包裹未处理仍在原处',
          longitude: 119.275515,
          latitude: 28.583248,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2114659',
          areaName: '申通、韵达、百世快递',
          longitude: 119.28,
          latitude: 28.5779,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2223796',
          areaName: '上南门—号百货副食品店梦翔',
          longitude: 119.278,
          latitude: 28.5776,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2506424',
          areaName: '丽水遂昌县营业部',
          longitude: 119.276,
          latitude: 28.5921,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2692399',
          areaName: '新居花园中邮驿站',
          longitude: 119.267,
          latitude: 28.5825,
          sum_num: '76',
          out_num: '54',
          order_num: '0',
        },
        {
          cm_id: '2701066',
          areaName: '遂昌中邮总站',
          longitude: 119.311,
          latitude: 28.6179,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2757463',
          areaName: '两山大厦A楼中邮驿站',
          longitude: 119.276,
          latitude: 28.5921,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2770984',
          areaName: '华鸿锦园中邮驿站',
          longitude: 119.247,
          latitude: 28.5499,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2815535',
          areaName: '电信小店',
          longitude: 119.277,
          latitude: 28.5922,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2915103',
          areaName: '上江邮政',
          longitude: 119.311,
          latitude: '28.6179',
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '2973901',
          areaName: '遂昌申通',
          longitude: 119.311,
          latitude: 28.6153,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3030630',
          areaName: '和泰碧桂园锦绣城',
          longitude: 119.261,
          latitude: 28.5779,
          sum_num: '71',
          out_num: '38',
          order_num: '0',
        },
        {
          cm_id: '3110192',
          areaName: '源口商店',
          longitude: 119.255,
          latitude: 28.5802,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3162974',
          areaName: '余家超市中邮驿站',
          longitude: 119.334,
          latitude: 28.6325,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3231863',
          areaName: '源口村中邮驿站',
          longitude: 119.251,
          latitude: 28.5682,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3387500',
          areaName: '中央首府中邮驿站',
          longitude: 119.27,
          latitude: 28.5842,
          sum_num: '49',
          out_num: '36',
          order_num: '0',
        },
      ],
      330703001000: [
        {
          cm_id: '1494459',
          areaName: '包裹未处理仍在原处',
          longitude: 119.707068,
          latitude: 29.081341,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '1494451',
          areaName: '包裹未处理仍在原处222',
          longitude: 119.742569,
          latitude: 29.078879,
          sum_num: '10',
          out_num: '20',
          order_num: '30',
        },
        {
          cm_id: '1494454',
          areaName: '包裹未处理仍在原处22233333',
          longitude: 119.676954,
          latitude: 29.127822,
          sum_num: '10',
          out_num: '20',
          order_num: '30',
        },
        {
          cm_id: '1494453',
          areaName: '4444',
          longitude: 119.690567,
          latitude: 29.118202,
          sum_num: '10',
          out_num: '20',
          order_num: '30',
        },
      ],
      1976: [
        {
          city_id: '331123001000',
          areaName: '妙高街道',
          sum_num: '19',
          order_num: '111',
          out_num: 60,
          count: '29',
          code: 331123001000,
        },
      ],

      330702: [
        {
          count: '1',
          areaName: '城东街道',
          code: '330702001000',
          sum_num: 0,
          out_num: 0,
          city_id: '330702001000',
          order_num: '0',
        },
        {
          count: '3',
          areaName: '城中街道',
          code: '330702002000',
          sum_num: 0,
          out_num: 0,
          city_id: '330702002000',
          order_num: '0',
        },
        {
          count: '2',
          areaName: '城西街道',
          code: '330702003000',
          sum_num: 0,
          out_num: 0,
          city_id: '330702003000',
          order_num: '0',
        },
        {
          count: '17',
          areaName: '城北街道',
          code: '330702004000',
          sum_num: 1379,
          out_num: 1235,
          city_id: '330702004000',
          order_num: '0',
        },
        {
          count: '5',
          areaName: '江南街道',
          code: '330702005000',
          sum_num: 373,
          out_num: 378,
          city_id: '330702005000',
          order_num: '0',
        },
        {
          count: '14',
          areaName: '三江街道',
          code: '330702006000',
          sum_num: 298,
          out_num: 262,
          city_id: '330702006000',
          order_num: '0',
        },
        {
          count: '7',
          areaName: '西关街道',
          code: '330702007000',
          sum_num: 10062,
          out_num: 9073,
          city_id: '330702007000',
          order_num: '2',
        },
        {
          count: '25',
          areaName: '秋滨街道',
          code: '330702008000',
          sum_num: 1132,
          out_num: 992,
          city_id: '330702008000',
          order_num: '1',
        },
        {
          count: '13',
          areaName: '新狮街道',
          code: '330702009000',
          sum_num: 2025,
          out_num: 1583,
          city_id: '330702009000',
          order_num: '0',
        },
        {
          count: '11',
          areaName: '罗店镇',
          code: '330702100000',
          sum_num: 693,
          out_num: 1305,
          city_id: '330702100000',
          order_num: '0',
        },
        {
          count: '2',
          areaName: '雅畈镇',
          code: '330702101000',
          sum_num: 14,
          out_num: 4,
          city_id: '330702101000',
          order_num: '0',
        },
        {
          count: '2',
          areaName: '安地镇',
          code: '330702102000',
          sum_num: 1,
          out_num: 1,
          city_id: '330702102000',
          order_num: '0',
        },
        {
          count: '25',
          areaName: '白龙桥镇',
          code: '330702103000',
          sum_num: 1546,
          out_num: 1431,
          city_id: '330702103000',
          order_num: '0',
        },
        {
          count: '1',
          areaName: '琅琊镇',
          code: '330702104000',
          sum_num: 498,
          out_num: 493,
          city_id: '330702104000',
          order_num: '0',
        },
        {
          count: '3',
          areaName: '蒋堂镇',
          code: '330702105000',
          sum_num: 56,
          out_num: 70,
          city_id: '330702105000',
          order_num: '0',
        },
        {
          count: '10',
          areaName: '汤溪镇',
          code: '330702106000',
          sum_num: 165,
          out_num: 141,
          city_id: '330702106000',
          order_num: '0',
        },
        {
          count: '3',
          areaName: '罗埠镇',
          code: '330702107000',
          sum_num: 77,
          out_num: 77,
          city_id: '330702107000',
          order_num: '0',
        },
        {
          count: '1',
          areaName: '洋埠镇',
          code: '330702108000',
          sum_num: 474,
          out_num: 434,
          city_id: '330702108000',
          order_num: '0',
        },
        {
          count: '5',
          areaName: '乾西乡',
          code: '330702201000',
          sum_num: 314,
          out_num: 293,
          city_id: '330702201000',
          order_num: '0',
        },
        {
          count: '1',
          areaName: '竹马乡',
          code: '330702202000',
          sum_num: 0,
          out_num: 0,
          city_id: '330702202000',
          order_num: '0',
        },
        {
          count: '1',
          areaName: '长山乡',
          code: '330702203000',
          sum_num: 3,
          out_num: 0,
          city_id: '330702203000',
          order_num: '0',
        },
        {
          count: '2',
          areaName: '塔石乡',
          code: '330702206000',
          sum_num: 0,
          out_num: 0,
          city_id: '330702206000',
          order_num: '0',
        },
        {
          count: '1',
          areaName: '莘畈乡',
          code: '330702208000',
          sum_num: 5,
          out_num: 3,
          city_id: '330702208000',
          order_num: '0',
        },
        {
          count: '3',
          areaName: '苏孟乡',
          code: '330702209000',
          sum_num: 128,
          out_num: 156,
          city_id: '330702209000',
          order_num: '0',
        },
      ],

      330702004000: [
        {
          cm_id: '2940006',
          areaName: '北苑小区中邮驿站',
          longitude: 119.651,
          latitude: 29.1169,
          sum_num: '149',
          out_num: '29',
          order_num: '0',
        },
        {
          cm_id: '3332010',
          areaName: '军营驿站南区',
          longitude: 119.639,
          latitude: 29.1156,
          sum_num: '255',
          out_num: '575',
          order_num: '0',
        },
        {
          cm_id: '3334982',
          areaName: '邮政大楼中邮驿站',
          longitude: 119.633,
          latitude: 29.1091,
          sum_num: '20',
          out_num: '9',
          order_num: '0',
        },
        {
          cm_id: '3683835',
          areaName: '家美超市',
          longitude: 119.644,
          latitude: 29.111,
          sum_num: '2',
          out_num: '2',
          order_num: '0',
        },
        {
          cm_id: '3693181',
          areaName: '联球大厦中邮驿站',
          longitude: 119.639,
          latitude: 29.1079,
          sum_num: '2',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3696176',
          areaName: '安宁烟酒中邮驿站',
          longitude: 119.633,
          latitude: 29.1076,
          sum_num: '2',
          out_num: '2',
          order_num: '0',
        },
        {
          cm_id: '3703515',
          areaName: '御景花园',
          longitude: 119.633,
          latitude: 29.1083,
          sum_num: '25',
          out_num: '7',
          order_num: '0',
        },
        {
          cm_id: '3708705',
          areaName: '银河花园中邮驿站',
          longitude: 119.634,
          latitude: 29.1095,
          sum_num: '5',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3748329',
          areaName: '城北街道后城里街399号',
          longitude: 119.637,
          latitude: 29.1097,
          sum_num: '0',
          out_num: '0',
          order_num: '0',
        },
        {
          cm_id: '3774355',
          areaName: '金竹苑中邮驿站',
          longitude: 119.62,
          latitude: 29.1056,
          sum_num: '85',
          out_num: '44',
          order_num: '0',
        },
      ],

      1507: [
        {
          count: '1',
          areaName: '维新乡',
          code: '000000000000',
          sum_num: 0,
          out_num: 3,
          city_id: '000000000000',
          order_num: '0',
        },
        {
          count: '9',
          areaName: '安文镇',
          code: '330727100000',
          sum_num: 0,
          out_num: 494,
          city_id: '330727100000',
          order_num: '0',
        },
        {
          count: '2',
          areaName: '新渥街道',
          code: '330727101000',
          sum_num: 0,
          out_num: 277,
          city_id: '330727101000',
          order_num: '0',
        },
        {
          count: '4',
          areaName: '尖山镇',
          code: '330727102000',
          sum_num: 0,
          out_num: 249,
          city_id: '330727102000',
          order_num: '0',
        },
        {
          count: '1',
          areaName: '仁川镇',
          code: '330727103000',
          sum_num: 0,
          out_num: 0,
          city_id: '330727103000',
          order_num: '0',
        },
        {
          count: '2',
          areaName: '大盘镇',
          code: '330727104000',
          sum_num: 667,
          out_num: 396,
          city_id: '330727104000',
          order_num: '0',
        },
        {
          count: '1',
          areaName: '方前镇',
          code: '330727105000',
          sum_num: 343,
          out_num: 493,
          city_id: '330727105000',
          order_num: '0',
        },
        {
          count: '1',
          areaName: '玉山镇',
          code: '330727106000',
          sum_num: 381,
          out_num: 342,
          city_id: '330727106000',
          order_num: '0',
        },
        {
          count: '2',
          areaName: '尚湖镇',
          code: '330727107000',
          sum_num: 415,
          out_num: 611,
          city_id: '330727107000',
          order_num: '0',
        },
        {
          count: '1',
          areaName: '冷水镇',
          code: '330727108000',
          sum_num: 0,
          out_num: 0,
          city_id: '330727108000',
          order_num: '0',
        },
        {
          count: '1',
          areaName: '双峰乡',
          code: '330727201000',
          sum_num: 0,
          out_num: 4,
          city_id: '330727201000',
          order_num: '0',
        },
        {
          count: '12',
          areaName: '双溪乡',
          code: '330727203000',
          sum_num: 523,
          out_num: 515,
          city_id: '330727203000',
          order_num: '0',
        },
        {
          count: '4',
          areaName: '窈川乡',
          code: '330727205000',
          sum_num: 592,
          out_num: 852,
          city_id: '330727205000',
          order_num: '0',
        },
        {
          count: '2',
          areaName: '盘峰乡',
          code: '330727206000',
          sum_num: 0,
          out_num: 0,
          city_id: '330727206000',
          order_num: '0',
        },
        {
          count: '1',
          areaName: '九和乡',
          code: '330727211000',
          sum_num: 0,
          out_num: 21,
          city_id: '330727211000',
          order_num: '0',
        },
      ],
      331024: [
        {
          count: '1',
          areaName: '安洲街道',
          code: '331024001000',
          sum_num: 1000,
          out_num: 21,
          city_id: '331024001000',
          order_num: '1000',
        },
      ],
    };

    res.send({
      code: 0,
      msg: 'success',
      data: mapData[code],
    });
  },
  'POST /Api/Statistics/getStageNumSevenDays': (req, res) => {
    const arr = Array.from(
      {
        length: 7,
      },
      (_, index) => ({
        date: `2021-12-1${index}`,
        num: randomValue(800, 100000),
        cabinet: randomValue(800, 100000),
      }),
    );

    res.send({
      code: 0,
      msg: 'success',
      data: arr,
    });
  },
  'POST /Api/Statistics/getFansNumSevenDays': (req, res) => {
    const arr = Array.from(
      {
        length: 7,
      },
      (_, index) => ({
        date: `2021-12-1${index}`,
        num: randomValue(800, 100000),
      }),
    );

    res.send({
      code: 0,
      msg: 'success',
      data: arr,
    });
  },
  'POST /Api/ShareLink/getVideoPageConf': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        shop_id: '7',
        page_conf: {
          switch: '1',
          cm_list: [
            {
              name: '断小弦儿',
              cm_id: 1748861,
            },
            {
              name: '断小弦儿',
              cm_id: 1748862,
            },
            {
              name: '断小弦儿',
              cm_id: 1748863,
            },
            {
              name: '断小弦儿',
              cm_id: 1748864,
            },
            {
              name: '断小弦儿',
              cm_id: 1748865,
            },
          ],
          show_num: '4',
        },
      },
    });
  },
  'POST /Api/Statistics/getVideoPageConf': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        shop_id: '7',
        page_conf: {
          switch: 1,
          cm_list: [
            {
              name: '断小弦儿',
              cm_id: 1748861,
            },
            {
              name: '断小弦儿',
              cm_id: 1748862,
            },
            {
              name: '断小弦儿',
              cm_id: 1748863,
            },
            {
              name: '断小弦儿',
              cm_id: 1748864,
            },
            {
              name: '断小弦儿',
              cm_id: 1748865,
            },
          ],
          show_num: '4',
        },
      },
    });
  },
  'POST /Api/Statistics/getStationSevenDayStat': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          league_id: '1243668',
          date: '2024-12-24',
          new_station_num: 0, // 新增站点数
          total_station_num: 0, // 总站点数
          in_station_num: 0, // 入库站点数
          new_cabinet_num: 3,
        },
        {
          league_id: '1243668',
          date: '2024-12-25',
          new_station_num: 0,
          total_station_num: 0,
          in_station_num: 0,
          new_cabinet_num: 1,
        },
        {
          league_id: '1243668',
          date: '2024-12-26',
          new_station_num: 0,
          total_station_num: 0,
          in_station_num: 0,
          new_cabinet_num: 1,
        },
        {
          league_id: '1243668',
          date: '2024-12-27',
          new_station_num: 0,
          total_station_num: 0,
          in_station_num: 0,
          new_cabinet_num: 1,
        },
        {
          league_id: '1243668',
          date: '2024-12-28',
          new_station_num: 0,
          total_station_num: 0,
          in_station_num: 0,
          new_cabinet_num: 1,
        },
        {
          league_id: '1243668',
          date: '2024-12-29',
          new_station_num: '0',
          total_station_num: '2',
          in_station_num: '0',
          new_cabinet_num: 1,
        },
        {
          league_id: '1243668',
          date: '2024-12-30',
          new_station_num: 0,
          total_station_num: 0,
          in_station_num: 0,
          new_cabinet_num: 2,
        },
      ],
    });
  },
};
